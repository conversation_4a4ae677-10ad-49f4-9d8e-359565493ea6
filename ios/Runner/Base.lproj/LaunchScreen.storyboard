<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Ydg-fD-yQy"/>
                        <viewControllerLayoutGuide type="bottom" id="xbc-2k-c8Z"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" image="LaunchImage" translatesAutoresizingMaskIntoConstraints="NO" id="YRO-k0-Ey4">
                                <rect key="frame" x="196.33333333333334" y="426" width="0.33333333333334281" height="0.33333333333331439"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="app_logo" translatesAutoresizingMaskIntoConstraints="NO" id="phc-jP-Z0M">
                                <rect key="frame" x="106.66666666666669" y="198" width="180" height="200"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="200" id="vDT-vE-aKH"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Chat with AI role" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g5V-yV-XQe">
                                <rect key="frame" x="125" y="706" width="143" height="24"/>
                                <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Meeloo" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3Hg-07-G6U">
                                <rect key="frame" x="135" y="639" width="123.33333333333331" height="43"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="36"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="YRO-k0-Ey4" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="1a2-6s-vTC"/>
                            <constraint firstItem="YRO-k0-Ey4" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="4X2-HB-R7a"/>
                            <constraint firstItem="3Hg-07-G6U" firstAttribute="centerX" secondItem="phc-jP-Z0M" secondAttribute="centerX" id="9oB-tl-Ceh"/>
                            <constraint firstItem="3Hg-07-G6U" firstAttribute="centerX" secondItem="g5V-yV-XQe" secondAttribute="centerX" id="LhU-ie-eeR"/>
                            <constraint firstItem="phc-jP-Z0M" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="topMargin" constant="80" id="NcG-fQ-3It"/>
                            <constraint firstItem="g5V-yV-XQe" firstAttribute="top" secondItem="3Hg-07-G6U" secondAttribute="bottom" constant="24" id="gqS-iH-HYj"/>
                            <constraint firstAttribute="bottom" secondItem="3Hg-07-G6U" secondAttribute="bottom" constant="170" id="qUV-Wc-nVI"/>
                            <constraint firstItem="3Hg-07-G6U" firstAttribute="centerX" secondItem="YRO-k0-Ey4" secondAttribute="centerX" id="tZ3-9S-F4h"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="80.152671755725194" y="264.08450704225356"/>
        </scene>
    </scenes>
    <resources>
        <image name="LaunchImage" width="0.3333333432674408" height="0.3333333432674408"/>
        <image name="app_logo" width="180" height="180"/>
    </resources>
</document>
