# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# Visual Studio Code related
.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS related
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# iOS Xcode related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/**/.generated/
**/ios/**/*.xcworkspace/xcuserdata/
**/ios/**/*.xcodeproj/xcuserdata/
**/ios/**/*.xcworkspace/xcshareddata/
**/ios/**/*.xcodeproj/project.xcworkspace/xcuserdata/
**/ios/**/dgph
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/

# Android related
**/android/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
**/android/key.properties
**/android/**/*.keystore
**/android/**/*.jks

# macOS related
**/macos/Flutter/ephemeral/
**/macos/Pods/
**/macos/dgph
**/macos/xcuserdata/

# Windows related
**/windows/flutter/ephemeral/
**/windows/*.suo
**/windows/*.user
**/windows/*.userosscache
**/windows/*.sln.docstates
**/windows/x64/
**/windows/x86/
**/windows/*.[Cc]ache
# but keep track of directories ending in .cache
!**/windows/*.[Cc]ache/

# Linux related
**/linux/flutter/ephemeral

# Web related
**/web/packages/

# Coverage
coverage/

# Symbols
app.*.symbols

# Exceptions to above rules.
!**/ios/**/default.mode1v3
!**/ios/**/default.mode2v3
!**/ios/**/default.pbxuser
!**/ios/**/default.perspectivev3 