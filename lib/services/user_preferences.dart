import 'package:shared_preferences/shared_preferences.dart';

class UserPreferences {
  static UserPreferences? _instance;
  static SharedPreferences? _preferences;

  static const String _keyUsername = 'username';
  static const String _keyThemeMode = 'theme_mode';
  static const String _keyLanguage = 'language';
  static const String _keyAvatar = 'avatar';

  UserPreferences._internal();

  static UserPreferences get instance {
    _instance ??= UserPreferences._internal();
    return _instance!;
  }

  static Future<void> init() async {
    _preferences = await SharedPreferences.getInstance();
  }

  // Username
  Future<void> setUsername(String username) async {
    await _preferences?.setString(_keyUsername, username);
  }

  String getUsername() {
    return _preferences?.getString(_keyUsername) ?? 'UserName';
  }

  // Theme Mode
  Future<void> setThemeMode(String themeMode) async {
    await _preferences?.setString(_keyThemeMode, themeMode);
  }

  String getThemeMode() {
    return _preferences?.getString(_keyThemeMode) ?? 'system';
  }

  // Language
  Future<void> setLanguage(String language) async {
    await _preferences?.setString(_keyLanguage, language);
  }

  String getLanguage() {
    return _preferences?.getString(_keyLanguage) ?? 'en';
  }

  // Avatar
  Future<void> setAvatar(String avatarPath) async {
    await _preferences?.setString(_keyAvatar, avatarPath);
  }

  String getAvatar() {
    return _preferences?.getString(_keyAvatar) ?? '';
  }

  // Clear all preferences
  Future<void> clearAll() async {
    await _preferences?.clear();
  }
} 