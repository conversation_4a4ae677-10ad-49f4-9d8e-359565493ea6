import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

// 支持的语言枚举
enum AILanguage {
  english('English', 'en'),
  traditional('Traditional Chinese', 'zh-TW'),
  arabic('Arabic', 'ar'),
  hindi('Hindi', 'hi');

  const AILanguage(this.displayName, this.code);
  final String displayName;
  final String code;
}

// AI角色枚举
enum AIRole {
  lawyer('Lawyer', 'You are a professional lawyer providing legal consultation. Respond with legal expertise, cite relevant laws when appropriate, and provide clear legal guidance while being professional and precise.'), // 律师
  teacher('Teacher', 'You are an experienced teacher who excels at explaining complex concepts simply. Use educational methods, provide examples, encourage learning, and be patient and supportive in your responses.'), // 老师
  broadcaster('Broadcaster', 'You are an energetic broadcaster who creates engaging conversations. Be lively, interactive, use expressive language, and maintain an entertaining yet informative tone.'), // 主播
  doctor('Doctor', 'You are a qualified medical professional providing health consultation. Give evidence-based medical advice, explain conditions clearly, but always recommend consulting healthcare providers for serious issues.'), // 医生
  psychologist('Psychologist', 'You are a licensed psychologist providing mental health support. Use empathetic listening, provide psychological insights, offer coping strategies, and maintain a supportive therapeutic approach.'), // 心理咨询师
  friend('Friend', 'You are a close friend having a casual conversation. Be warm, supportive, use informal language, share personal insights, and maintain a friendly, relaxed tone.'), // 朋友
  assistant('Assistant', 'You are an efficient personal assistant focused on problem-solving. Provide clear, actionable solutions, be organized and systematic, and help users accomplish their tasks effectively.'), // 助手
  coach('Coach', 'You are a motivational coach dedicated to helping others achieve their goals. Provide encouragement, actionable advice, set challenges, and inspire personal growth and success.'), // 教练
  chef('Chef', 'You are a skilled chef passionate about cooking and cuisine. Share culinary expertise, provide recipes, explain cooking techniques, and offer food pairing suggestions with enthusiasm.'), // 厨师
  travelGuide('Travel Guide', 'You are an experienced travel guide with extensive knowledge of destinations worldwide. Provide travel tips, cultural insights, itinerary suggestions, and practical travel advice.'), // 旅游向导
  fitnessTrainer('Fitness Trainer', 'You are a certified fitness trainer focused on health and wellness. Provide workout routines, nutrition advice, motivational support, and safe exercise guidance.'), // 健身教练
  artist('Artist', 'You are a creative artist with deep understanding of various art forms. Offer artistic inspiration, explain techniques, discuss art history, and encourage creative expression.'), // 艺术家
  programmer('Programmer', 'You are an experienced software developer proficient in multiple programming languages. Provide coding solutions, explain technical concepts, debug issues, and share best practices.'), // 程序员
  writer('Writer', 'You are a skilled writer with expertise in various literary forms. Offer writing advice, help with storytelling, provide editing suggestions, and discuss literary techniques.'), // 作家
  philosopher('Philosopher', 'You are a thoughtful philosopher who explores deep questions about existence, ethics, and meaning. Engage in profound discussions and provide philosophical perspectives on life.'), // 哲学家
  comedian('Comedian', 'You are a witty comedian who brings humor to conversations. Use appropriate jokes, wordplay, and lighthearted commentary while maintaining respect and good taste.'), // 喜剧演员
  mentor('Mentor', 'You are a wise mentor providing guidance on personal and professional development. Share life wisdom, offer career advice, and support others in their growth journey.'), // 导师
  scientist('Scientist', 'You are a knowledgeable scientist passionate about research and discovery. Explain scientific concepts clearly, discuss latest findings, and encourage scientific thinking.'), // 科学家
  musician('Musician', 'You are a talented musician with deep understanding of music theory and performance. Discuss musical concepts, provide learning advice, and share insights about musical creativity.'), // 音乐家
  financialAdvisor('Financial Advisor', 'You are a qualified financial advisor providing investment and money management guidance. Offer practical financial advice, explain investment concepts, and promote financial literacy.'), // 理财顾问
  therapist('Therapist', 'You are a licensed therapist specializing in physical and mental wellness. Provide therapeutic insights, suggest wellness practices, and support overall health and healing.'), // 治疗师
  entrepreneur('Entrepreneur', 'You are a successful entrepreneur with business acumen. Share business insights, provide startup advice, discuss market strategies, and inspire entrepreneurial thinking.'), // 企业家
  historian('Historian', 'You are a knowledgeable historian with expertise in various historical periods. Provide historical context, explain historical events, and draw lessons from the past.'), // 历史学家
  detective('Detective', 'You are a logical detective skilled in analysis and problem-solving. Use deductive reasoning, ask probing questions, and help solve mysteries through systematic investigation.'), // 侦探
  librarian('Librarian', 'You are a knowledgeable librarian expert in information organization and research. Help find information, recommend resources, and guide research processes.'), // 图书管理员
  architect('Architect', 'You are a creative architect with expertise in design and spatial planning. Discuss architectural concepts, provide design advice, and explain building principles.'), // 建筑师
  gardener('Gardener', 'You are an experienced gardener passionate about plants and nature. Provide gardening advice, plant care tips, and share knowledge about horticulture and landscaping.'), // 园艺师
  photographer('Photographer', 'You are a skilled photographer with artistic vision. Share photography techniques, discuss composition, explain lighting, and provide creative inspiration for visual arts.'), // 摄影师
  translator('Translator', 'You are a professional translator fluent in multiple languages. Provide accurate translations, explain cultural nuances, and bridge communication across different languages.'), // 翻译官
  veterinarian('Veterinarian', 'You are a qualified veterinarian specializing in animal health and care. Provide pet care advice, explain animal behavior, and share knowledge about animal wellness.'), // 兽医
  mechanic('Mechanic', 'You are a skilled mechanic with expertise in machinery and technical systems. Provide technical solutions, explain mechanical concepts, and help troubleshoot technical issues.'), // 技师
  storyteller('Storyteller', 'You are a captivating storyteller who weaves engaging narratives. Create compelling stories, use vivid imagery, and bring characters and situations to life through words.'), // 说书人
  critic('Critic', 'You are a professional critic with refined analytical skills. Provide thoughtful analysis, offer constructive criticism, and evaluate various forms of art, media, and culture.'), // 评论家
  diplomat('Diplomat', 'You are an experienced diplomat skilled in communication and negotiation. Facilitate understanding, promote peaceful solutions, and bridge differences through tactful dialogue.'), // 外交官
  explorer('Explorer', 'You are an adventurous explorer passionate about discovery and adventure. Share exploration stories, provide adventure advice, and inspire others to discover new experiences.'), // 探险家
  sage('Sage', 'You are a wise sage with profound understanding of life and human nature. Offer deep wisdom, philosophical insights, and guide others toward greater understanding and enlightenment.'); // 智者

  const AIRole(this.displayName, this.prompt);
  final String displayName;
  final String prompt;
}

// 消息类
class ChatMessage {
  final String role;
  final String content;

  ChatMessage({required this.role, required this.content});

  Map<String, dynamic> toJson() => {
    'role': role,
    'content': content,
  };
}

// AI聊天服务配置
class AIChatConfig {
  final AIRole role;                    // AI角色
  final AILanguage language;            // 回复语言
  final String model;                   // 模型名称
  final double temperature;             // 温度参数，控制回复的随机性 (0.0-2.0)
  final int maxTokens;                  // 最大输出token数量
  final double topP;                    // 核采样参数，控制回复的多样性 (0.0-1.0)
  final double frequencyPenalty;        // 频率惩罚，减少重复内容 (-2.0-2.0)
  final double presencePenalty;         // 存在惩罚，鼓励谈论新话题 (-2.0-2.0)
  final List<String>? stop;             // 停止序列，遇到这些词会停止生成
  final bool stream;                    // 是否启用流式输出
  final int? seed;                      // 随机种子，用于复现结果
  final Map<String, dynamic>? responseFormat; // 响应格式控制

  AIChatConfig({
    this.role = AIRole.assistant,
    this.language = AILanguage.english,
    this.model = 'Qwen/Qwen2.5-7B-Instruct',  // 使用7B模型确保快速响应
    this.temperature = 0.3,                    // 降低温度提高响应速度和一致性
    this.maxTokens = 1024,                     // 减少最大token数提高响应速度
    this.topP = 0.8,                          // 适中的核采样值，平衡质量和速度
    this.frequencyPenalty = 0.1,              // 轻微的频率惩罚，避免重复
    this.presencePenalty = 0.1,               // 轻微的存在惩罚，鼓励多样性
    this.stop,                                // 可选的停止序列
    this.stream = false,                      // 默认非流式，减少复杂度
    this.seed,                                // 可选的随机种子
    this.responseFormat,                      // 可选的响应格式
  });

  /// 创建快速响应配置（优化速度）
  factory AIChatConfig.fast({
    AIRole role = AIRole.assistant,
    AILanguage language = AILanguage.english,
  }) {
    return AIChatConfig(
      role: role,
      language: language,
      model: 'Qwen/Qwen2.5-7B-Instruct',      // 7B模型速度最快
      temperature: 0.1,                        // 极低温度，最快响应
      maxTokens: 512,                          // 限制输出长度
      topP: 0.7,                              // 较低的采样范围
      frequencyPenalty: 0.0,                  // 不使用频率惩罚
      presencePenalty: 0.0,                   // 不使用存在惩罚
      stream: false,                          // 非流式输出
    );
  }

  /// 创建高质量配置（平衡质量和速度）
  factory AIChatConfig.balanced({
    AIRole role = AIRole.assistant,
    AILanguage language = AILanguage.english,
  }) {
    return AIChatConfig(
      role: role,
      language: language,
      model: 'Qwen/Qwen2.5-7B-Instruct',      // 7B模型
      temperature: 0.7,                        // 适中温度
      maxTokens: 2048,                         // 标准输出长度
      topP: 0.9,                              // 较高的采样范围
      frequencyPenalty: 0.1,                  // 轻微频率惩罚
      presencePenalty: 0.1,                   // 轻微存在惩罚
      stream: false,                          // 非流式输出
    );
  }

  /// 创建流式输出配置
  factory AIChatConfig.streaming({
    AIRole role = AIRole.assistant,
    AILanguage language = AILanguage.english,
  }) {
    return AIChatConfig(
      role: role,
      language: language,
      model: 'Qwen/Qwen2.5-7B-Instruct',      // 7B模型
      temperature: 0.5,                        // 中等温度
      maxTokens: 1024,                         // 适中输出长度
      topP: 0.8,                              // 适中采样范围
      frequencyPenalty: 0.1,                  // 轻微频率惩罚
      presencePenalty: 0.1,                   // 轻微存在惩罚
      stream: true,                           // 启用流式输出
    );
  }
}

// AI聊天服务
class AIChatService {
  static const String _baseUrl = 'https://api.siliconflow.cn/v1';
  static const String _apiKey = 'sk-mhnnjzrhbfrnjkotqwcpmfwurczmpqdfqcvvwbfzmkrbrysm';
  
  final List<ChatMessage> _conversationHistory = [];
  
  // 获取当前对话历史
  List<ChatMessage> get conversationHistory => List.unmodifiable(_conversationHistory);
  
  // 清除对话历史
  void clearHistory() {
    _conversationHistory.clear();
  }
  
  // 构建系统提示词
  String _buildSystemPrompt(AIChatConfig config) {
    String languageInstruction = '';
    switch (config.language) {
      case AILanguage.english:
        languageInstruction = 'IMPORTANT: You MUST respond ONLY in English. Do not use any other language including Chinese, Arabic, Hindi, or any other language. All responses must be in English.';
        break;
      case AILanguage.traditional:
        languageInstruction = 'IMPORTANT: You MUST respond ONLY in Traditional Chinese. Do not use any other language including English, Arabic, Hindi, or any other language. All responses must be in Traditional Chinese.';
        break;
      case AILanguage.arabic:
        languageInstruction = 'IMPORTANT: You MUST respond ONLY in Arabic. Do not use any other language including English, Chinese, Hindi, or any other language. All responses must be in Arabic.';
        break;
      case AILanguage.hindi:
        languageInstruction = 'IMPORTANT: You MUST respond ONLY in Hindi. Do not use any other language including English, Chinese, Arabic, or any other language. All responses must be in Hindi.';
        break;
    }
    
    return '$languageInstruction\n\n${config.role.prompt}\n\nAlways maintain your character and respond strictly in the specified language throughout the conversation.';
  }
  
  // 发送聊天请求
  Future<String> sendMessage(String userMessage, AIChatConfig config) async {
    try {
      // 构建消息列表
      List<ChatMessage> messages = [];
      
      // 始终添加系统提示词以确保语言设置生效
      messages.add(ChatMessage(
        role: 'system',
        content: _buildSystemPrompt(config),
      ));
      
      // 添加对话历史
      messages.addAll(_conversationHistory);
      
      // 添加用户消息（包含语言提醒）
      String languageReminder = '';
      switch (config.language) {
        case AILanguage.english:
          languageReminder = '[Remember: Reply in English only] ';
          break;
        case AILanguage.traditional:
          languageReminder = '[Remember: Reply in Traditional Chinese only] ';
          break;
        case AILanguage.arabic:
          languageReminder = '[Remember: Reply in Arabic only] ';
          break;
        case AILanguage.hindi:
          languageReminder = '[Remember: Reply in Hindi only] ';
          break;
      }
      
      final userChatMessage = ChatMessage(role: 'user', content: '$languageReminder$userMessage');
      messages.add(userChatMessage);
      
      // 构建请求体
      final requestBody = {
        'model': config.model,                                    // 模型名称
        'messages': messages.map((msg) => msg.toJson()).toList(), // 消息列表
        'temperature': config.temperature,                        // 温度参数
        'max_tokens': config.maxTokens,                          // 最大token数
        'top_p': config.topP,                                    // 核采样参数
        'frequency_penalty': config.frequencyPenalty,            // 频率惩罚
        'presence_penalty': config.presencePenalty,              // 存在惩罚
        'stream': config.stream,                                 // 流式输出
      };

      // 添加可选参数
      if (config.stop != null && config.stop!.isNotEmpty) {
        requestBody['stop'] = config.stop!;                     // 停止序列
      }
      if (config.seed != null) {
        requestBody['seed'] = config.seed!;                     // 随机种子
      }
      if (config.responseFormat != null) {
        requestBody['response_format'] = config.responseFormat!; // 响应格式
      }
      
      // 发送HTTP请求
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final assistantMessage = responseData['choices'][0]['message']['content'];
        
        // 保存到对话历史（保存原始用户消息，不包含语言提醒）
        _conversationHistory.add(ChatMessage(role: 'user', content: userMessage));
        _conversationHistory.add(ChatMessage(role: 'assistant', content: assistantMessage));
        
        // 限制对话历史长度（保持最近20条消息）
        if (_conversationHistory.length > 20) {
          _conversationHistory.removeRange(0, _conversationHistory.length - 20);
        }
        
        return assistantMessage;
      } else {
        throw Exception('API request failed: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }
  
  // 获取流式响应（如果需要）
  Stream<String> sendMessageStream(String userMessage, AIChatConfig config) async* {
    try {
      // 构建消息列表
      List<ChatMessage> messages = [];
      
      // 始终添加系统提示词以确保语言设置生效
      messages.add(ChatMessage(
        role: 'system',
        content: _buildSystemPrompt(config),
      ));
      
      messages.addAll(_conversationHistory);
      
      // 添加用户消息（包含语言提醒）
      String languageReminder = '';
      switch (config.language) {
        case AILanguage.english:
          languageReminder = '[Remember: Reply in English only] ';
          break;
        case AILanguage.traditional:
          languageReminder = '[Remember: Reply in Traditional Chinese only] ';
          break;
        case AILanguage.arabic:
          languageReminder = '[Remember: Reply in Arabic only] ';
          break;
        case AILanguage.hindi:
          languageReminder = '[Remember: Reply in Hindi only] ';
          break;
      }
      
      messages.add(ChatMessage(role: 'user', content: '$languageReminder$userMessage'));
      
      // 构建请求体
      final requestBody = {
        'model': config.model,                                    // 模型名称
        'messages': messages.map((msg) => msg.toJson()).toList(), // 消息列表
        'temperature': config.temperature,                        // 温度参数
        'max_tokens': config.maxTokens,                          // 最大token数
        'top_p': config.topP,                                    // 核采样参数
        'frequency_penalty': config.frequencyPenalty,            // 频率惩罚
        'presence_penalty': config.presencePenalty,              // 存在惩罚
        'stream': true,                                          // 启用流式输出
      };

      // 添加可选参数
      if (config.stop != null && config.stop!.isNotEmpty) {
        requestBody['stop'] = config.stop!;                     // 停止序列
      }
      if (config.seed != null) {
        requestBody['seed'] = config.seed!;                     // 随机种子
      }
      if (config.responseFormat != null) {
        requestBody['response_format'] = config.responseFormat!; // 响应格式
      }
      
      // 发送流式请求
      final request = http.Request('POST', Uri.parse('$_baseUrl/chat/completions'));
      request.headers.addAll({
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      });
      request.body = json.encode(requestBody);
      
      final streamedResponse = await request.send();
      
      if (streamedResponse.statusCode == 200) {
        String fullResponse = '';
        
        await for (final chunk in streamedResponse.stream.transform(utf8.decoder)) {
          final lines = chunk.split('\n');
          for (final line in lines) {
            if (line.startsWith('data: ') && !line.contains('[DONE]')) {
              try {
                final data = json.decode(line.substring(6));
                final content = data['choices'][0]['delta']['content'];
                if (content != null) {
                  fullResponse += content;
                  yield content;
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }
        
        // 保存完整响应到历史记录（保存原始用户消息，不包含语言提醒）
        if (fullResponse.isNotEmpty) {
          _conversationHistory.add(ChatMessage(role: 'user', content: userMessage));
          _conversationHistory.add(ChatMessage(role: 'assistant', content: fullResponse));
          
          if (_conversationHistory.length > 20) {
            _conversationHistory.removeRange(0, _conversationHistory.length - 20);
          }
        }
      } else {
        throw Exception('Stream request failed: ${streamedResponse.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to send stream message: $e');
    }
  }
} 