import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/user.dart';

class AIDataService {
  static final AIDataService _instance = AIDataService._internal();
  static List<User>? _allUsers;
  static final Random _random = Random();

  factory AIDataService() {
    return _instance;
  }

  AIDataService._internal();

  /// Load all AI users from JSON file
  Future<List<User>> loadAllUsers() async {
    if (_allUsers != null) {
      return _allUsers!;
    }

    try {
      final String jsonString = await rootBundle.loadString('assets/data/ai_users.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> userList = jsonData['ai_users'];

      _allUsers = userList.map((userData) {
        return User(
          id: userData['id'],
          name: userData['name'],
          avatar: userData['avatar'],
          description: userData['description'],
          tags: List<String>.from(userData['tags']),
          personality: userData['personality'] ?? '',
          role: userData['role'] ?? 'assistant',
          isOnline: true, // All AI users are always online
        );
      }).toList();

      return _allUsers!;
    } catch (e) {
      debugPrint('Error loading AI users: $e');
      return [];
    }
  }

  /// Get random AI users for recommendations
  Future<List<User>> getRandomUsers({int count = 5}) async {
    final allUsers = await loadAllUsers();
    if (allUsers.isEmpty) return [];

    if (count >= allUsers.length) {
      return List.from(allUsers)..shuffle();
    }

    final shuffled = List<User>.from(allUsers);
    shuffled.shuffle(_random);
    return shuffled.take(count).toList();
  }

  /// Get a specific user by ID
  Future<User?> getUserById(String id) async {
    final allUsers = await loadAllUsers();
    try {
      return allUsers.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get all users
  Future<List<User>> getAllUsers() async {
    return await loadAllUsers();
  }

  /// Get users by personality type
  Future<List<User>> getUsersByPersonality(String personality) async {
    final allUsers = await loadAllUsers();
    if (personality.isEmpty) return [];
    
    final lowerPersonality = personality.toLowerCase();
    
    return allUsers.where((user) {
      return user.personality.toLowerCase().contains(lowerPersonality);
    }).toList();
  }

  /// Get users by role
  Future<List<User>> getUsersByRole(String role) async {
    final allUsers = await loadAllUsers();
    if (role.isEmpty) return [];
    
    return allUsers.where((user) {
      return user.role == role;
    }).toList();
  }

  /// Search users by name or profession
  Future<List<User>> searchUsers(String query) async {
    if (query.isEmpty) return [];
    
    final allUsers = await loadAllUsers();
    final lowerQuery = query.toLowerCase();
    
    return allUsers.where((user) {
      return user.name.toLowerCase().contains(lowerQuery) ||
             user.description.toLowerCase().contains(lowerQuery) ||
             user.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// Get trending users (random selection for demo)
  Future<List<User>> getTrendingUsers({int count = 8}) async {
    return getRandomUsers(count: count);
  }

  /// Get recommended users based on user interactions (random for demo)
  Future<List<User>> getRecommendedUsers({int count = 10}) async {
    return getRandomUsers(count: count);
  }

  /// Clear cache and reload data
  Future<void> refreshData() async {
    _allUsers = null;
    await loadAllUsers();
  }
} 