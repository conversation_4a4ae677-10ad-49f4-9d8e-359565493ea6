import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/message.dart';
import '../models/chat.dart';
import 'ai_data_service.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  static Database? _database;

  factory DatabaseService() {
    return _instance;
  }

  DatabaseService._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    String path = join(await getDatabasesPath(), 'ai_chat.db');
    return await openDatabase(
      path,
      version: 2,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        avatar TEXT NOT NULL,
        description TEXT NOT NULL,
        tags TEXT NOT NULL,
        personality TEXT NOT NULL DEFAULT '',
        role TEXT NOT NULL DEFAULT 'assistant',
        isOnline INTEGER NOT NULL DEFAULT 0
      )
    ''');

    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        chatId TEXT NOT NULL,
        senderId TEXT NOT NULL,
        receiverId TEXT NOT NULL,
        content TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        isRead INTEGER NOT NULL DEFAULT 0,
        type INTEGER NOT NULL DEFAULT 0
      )
    ''');

    await db.execute('''
      CREATE TABLE chats (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        aiId TEXT NOT NULL,
        lastMessage TEXT NOT NULL,
        lastMessageTime INTEGER NOT NULL,
        unreadCount INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Add personality and role columns
      await db.execute('ALTER TABLE users ADD COLUMN personality TEXT NOT NULL DEFAULT ""');
      await db.execute('ALTER TABLE users ADD COLUMN role TEXT NOT NULL DEFAULT "assistant"');
    }
  }

  // User operations (delegated to AIDataService)
  Future<List<User>> getUsers() async {
    return await AIDataService().getAllUsers();
  }

  Future<User?> getUserById(String id) async {
    return await AIDataService().getUserById(id);
  }

  // Message operations
  Future<void> insertMessage(Message message) async {
    final db = await database;
    await db.insert('messages', message.toMap());
  }

  Future<List<Message>> getMessagesForChat(String chatId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'messages',
      where: 'chatId = ?',
      whereArgs: [chatId],
      orderBy: 'timestamp ASC',
    );
    return List.generate(maps.length, (i) {
      return Message.fromMap(maps[i]);
    });
  }

  // Chat operations
  Future<void> insertOrUpdateChat(Chat chat) async {
    final db = await database;
    await db.insert(
      'chats',
      chat.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Chat>> getChats() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'chats',
      orderBy: 'lastMessageTime DESC',
    );
    return List.generate(maps.length, (i) {
      return Chat.fromMap(maps[i]);
    });
  }

  Future<Chat?> getChatByAiId(String aiId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'chats',
      where: 'aiId = ?',
      whereArgs: [aiId],
    );
    if (maps.isNotEmpty) {
      return Chat.fromMap(maps.first);
    }
    return null;
  }
} 