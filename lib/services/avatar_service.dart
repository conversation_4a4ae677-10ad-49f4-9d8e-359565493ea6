import 'package:flutter/services.dart';

class AvatarService {
  static const String _avatarBasePath = 'assets/images/avatars';
  
  /// 获取所有可用的头像路径列表
  static List<String> getAvailableAvatars() {
    // 基于实际的头像文件列表
    return [
      '$_avatarBasePath/avatar_1.png',
      '$_avatarBasePath/avatar_2.png',
      '$_avatarBasePath/avatar_3.png',
      '$_avatarBasePath/avatar_4.png',
      '$_avatarBasePath/avatar_5.png',
      '$_avatarBasePath/avatar_6.png',
      '$_avatarBasePath/avatar_7.png',
      '$_avatarBasePath/avatar_8.png',
      '$_avatarBasePath/avatar_9.png',
      '$_avatarBasePath/avatar_10.png',
      '$_avatarBasePath/avatar_11.png',
      '$_avatarBasePath/avatar_12.png',
      '$_avatarBasePath/avatar_13.png',
      '$_avatarBasePath/avatar_14.png',
      '$_avatarBasePath/avatar_15.png',
      '$_avatarBasePath/avatar_16.png',
      '$_avatarBasePath/avatar_17.png',
      '$_avatarBasePath/avatar_18.png',
    ];
  }

  /// 验证头像路径是否有效
  static bool isValidAvatarPath(String avatarPath) {
    return getAvailableAvatars().contains(avatarPath);
  }

  /// 获取头像的显示名称（从路径中提取）
  static String getAvatarDisplayName(String avatarPath) {
    if (avatarPath.isEmpty) return '';
    
    final fileName = avatarPath.split('/').last;
    final nameWithoutExtension = fileName.split('.').first;
    
    // 将 avatar_1 转换为 Avatar 1
    return nameWithoutExtension
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : word)
        .join(' ');
  }

  /// 检查头像资源是否存在
  static Future<bool> checkAvatarExists(String avatarPath) async {
    try {
      await rootBundle.load(avatarPath);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取默认头像路径
  static String getDefaultAvatar() {
    return '$_avatarBasePath/avatar_1.png';
  }
}
