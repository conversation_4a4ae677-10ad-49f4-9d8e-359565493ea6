import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';

/// 网格背景组件
/// 创建类似方格纸的装饰性背景效果
class GridBackground extends StatelessWidget {
  final Widget child;
  final double gridSize;
  final Color gridColor;
  final double strokeWidth;

  const GridBackground({
    super.key,
    required this.child,
    this.gridSize = 24.0,
    this.gridColor = const Color(0x08000000), // 非常淡的黑色
    this.strokeWidth = 0.5,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 网格背景
        Positioned.fill(
          child: CustomPaint(
            painter: GridPainter(
              gridSize: gridSize,
              gridColor: gridColor,
              strokeWidth: strokeWidth,
            ),
          ),
        ),
        // 子组件
        child,
      ],
    );
  }
}

/// 网格绘制器
class GridPainter extends CustomPainter {
  final double gridSize;
  final Color gridColor;
  final double strokeWidth;

  GridPainter({
    required this.gridSize,
    required this.gridColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = gridColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    // 绘制垂直线
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // 绘制水平线
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! GridPainter ||
        oldDelegate.gridSize != gridSize ||
        oldDelegate.gridColor != gridColor ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}