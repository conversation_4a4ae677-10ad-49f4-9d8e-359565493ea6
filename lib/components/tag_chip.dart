import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';

class TagChip extends StatelessWidget {
  final String tag;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;

  const TagChip({
    super.key,
    required this.tag,
    this.fontSize = 13,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    this.borderRadius = 18,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: AppDesignSystem.tagChipDecoration(
        AppDesignSystem.getTagColorByFirstLetter(tag),
      ),
      child: Text(
        tag,
        style: TextStyle(
          color: AppDesignSystem.textOnPrimary,
          fontSize: fontSize,
          fontWeight: AppDesignSystem.fontWeightMedium,
        ),
      ),
    );
  }
}

/// 标签列表组件
class TagChipList extends StatelessWidget {
  final List<String> tags;
  final int? maxTags;
  final double spacing;
  final double runSpacing;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;

  const TagChipList({
    super.key,
    required this.tags,
    this.maxTags,
    this.spacing = 8,
    this.runSpacing = 10,
    this.fontSize = 13,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    this.borderRadius = 18,
  });

  @override
  Widget build(BuildContext context) {
    final displayTags = maxTags != null ? tags.take(maxTags!).toList() : tags;
    
    return Wrap(
      spacing: spacing,
      runSpacing: runSpacing,
      children: displayTags.map((tag) {
        return TagChip(
          tag: tag,
          fontSize: fontSize,
          padding: padding,
          borderRadius: borderRadius,
        );
      }).toList(),
    );
  }
} 