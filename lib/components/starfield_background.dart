import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../theme/app_design_system.dart';

/// 2.5D星空背景组件
class StarfieldBackground extends StatefulWidget {
  final double width;
  final double height;
  
  const StarfieldBackground({
    super.key,
    required this.width,
    required this.height,
  });

  @override
  State<StarfieldBackground> createState() => _StarfieldBackgroundState();
}

class _StarfieldBackgroundState extends State<StarfieldBackground>
    with TickerProviderStateMixin {
  late AnimationController _twinkleController;
  late AnimationController _parallaxController;
  late Animation<double> _twinkleAnimation;
  late Animation<double> _parallaxAnimation;
  
  List<Star> _stars = [];
  
  @override
  void initState() {
    super.initState();
    
    // 星星闪烁动画控制器
    _twinkleController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    
    // 视差滚动动画控制器
    _parallaxController = AnimationController(
      duration: const Duration(seconds: 60),
      vsync: this,
    );
    
    _twinkleAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _twinkleController,
      curve: Curves.linear,
    ));
    
    _parallaxAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _parallaxController,
      curve: Curves.linear,
    ));
    
    _generateStars();
    
    // 启动动画循环
    _twinkleController.repeat();
    _parallaxController.repeat();
  }
  
  @override
  void dispose() {
    _twinkleController.dispose();
    _parallaxController.dispose();
    super.dispose();
  }
  
  void _generateStars() {
    final random = math.Random(42); // 固定种子确保星星位置一致
    _stars.clear();
    
    // 生成不同层级的星星
    for (int layer = 0; layer < 3; layer++) {
      final starCount = [150, 100, 50][layer]; // 不同层级的星星数量
      final baseSize = [1.0, 1.5, 2.5][layer]; // 不同层级的基础大小
      final speed = [0.3, 0.6, 1.0][layer]; // 不同层级的移动速度
      
      for (int i = 0; i < starCount; i++) {
        _stars.add(Star(
          x: random.nextDouble() * widget.width,
          y: random.nextDouble() * widget.height,
          size: baseSize + random.nextDouble() * 1.0,
          brightness: 0.3 + random.nextDouble() * 0.7,
          twinklePhase: random.nextDouble() * 2 * math.pi,
          layer: layer,
          speed: speed,
        ));
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      // decoration: const BoxDecoration(
      //   gradient: RadialGradient(
      //     center: Alignment.center,
      //     radius: 1.5,
      //     colors: [
      //       Color(0xFF0A0A2E), // 深蓝色中心
      //       Color(0xFF1A1A3A), // 中等蓝色
      //       Color(0xFF000011), // 深紫黑色边缘
      //     ],
      //     stops: [0.0, 0.6, 1.0],
      //   ),
      // ),
      color: Color(0xFF0A0A0A),
      child: AnimatedBuilder(
        animation: Listenable.merge([_twinkleAnimation, _parallaxAnimation]),
        builder: (context, child) {
          return CustomPaint(
            painter: StarfieldPainter(
              stars: _stars,
              twinkleValue: _twinkleAnimation.value,
              parallaxValue: _parallaxAnimation.value,
              width: widget.width,
              height: widget.height,
            ),
            size: Size(widget.width, widget.height),
          );
        },
      ),
    );
  }
}

/// 星星数据类
class Star {
  final double x;
  final double y;
  final double size;
  final double brightness;
  final double twinklePhase;
  final int layer;
  final double speed;
  
  const Star({
    required this.x,
    required this.y,
    required this.size,
    required this.brightness,
    required this.twinklePhase,
    required this.layer,
    required this.speed,
  });
}

/// 星空绘制器
class StarfieldPainter extends CustomPainter {
  final List<Star> stars;
  final double twinkleValue;
  final double parallaxValue;
  final double width;
  final double height;
  
  StarfieldPainter({
    required this.stars,
    required this.twinkleValue,
    required this.parallaxValue,
    required this.width,
    required this.height,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    for (final star in stars) {
      // 计算视差滚动位置
      final parallaxOffset = parallaxValue * star.speed * 20;
      final x = (star.x + parallaxOffset) % width;
      final y = star.y;
      
      // 计算闪烁效果
      final twinkle = math.sin(twinkleValue + star.twinklePhase);
      final currentBrightness = star.brightness * (0.5 + 0.5 * twinkle);
      
      // 绘制星星
      final paint = Paint()
        ..color = Colors.white.withOpacity(currentBrightness)
        ..style = PaintingStyle.fill;
      
      // 根据层级绘制不同效果的星星
      if (star.layer == 2) {
        // 最前层：带光晕的大星星
        _drawStarWithGlow(canvas, Offset(x, y), star.size, currentBrightness);
      } else {
        // 背景层：简单的圆点
        canvas.drawCircle(Offset(x, y), star.size, paint);
      }
    }
  }
  
  void _drawStarWithGlow(Canvas canvas, Offset center, double size, double brightness) {
    // 绘制光晕
    final glowPaint = Paint()
      ..color = AppDesignSystem.primaryYellow.withOpacity(brightness * 0.3)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);
    
    canvas.drawCircle(center, size * 2, glowPaint);
    
    // 绘制星星核心
    final corePaint = Paint()
      ..color = Colors.white.withOpacity(brightness)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, size, corePaint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
