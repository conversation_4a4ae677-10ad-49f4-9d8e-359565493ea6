import 'package:flutter/material.dart';

class ActionSheetAction {
  final String title;
  final VoidCallback onPressed;
  final Color? color;
  final IconData? icon;
  final bool isDestructive;
  final bool isCancel;

  const ActionSheetAction({
    required this.title,
    required this.onPressed,
    this.color,
    this.icon,
    this.isDestructive = false,
    this.isCancel = false,
  });
}

class ActionSheet {
  static Future<void> show({
    required BuildContext context,
    String? title,
    String? message,
    required List<ActionSheetAction> actions,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return _ActionSheetWidget(
          title: title,
          message: message,
          actions: actions,
        );
      },
    );
  }
}

class _ActionSheetWidget extends StatelessWidget {
  final String? title;
  final String? message;
  final List<ActionSheetAction> actions;

  const _ActionSheetWidget({
    this.title,
    this.message,
    required this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final cancelActions = actions.where((action) => action.isCancel).toList();
    final regularActions = actions.where((action) => !action.isCancel).toList();

    return SafeArea(
      child: Container(
        margin: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Main action sheet
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(14),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Title and message
                  if (title != null || message != null) ...[
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.fromLTRB(16, 20, 16, 12),
                      child: Column(
                        children: [
                          if (title != null)
                            Text(
                              title!,
                              style: const TextStyle(
                                color: Color(0xFF8E8E93),
                                fontSize: 13,
                                fontWeight: FontWeight.w400,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          if (title != null && message != null)
                            const SizedBox(height: 4),
                          if (message != null)
                            Text(
                              message!,
                              style: const TextStyle(
                                color: Color(0xFF8E8E93),
                                fontSize: 13,
                                fontWeight: FontWeight.w400,
                              ),
                              textAlign: TextAlign.center,
                            ),
                        ],
                      ),
                    ),
                    Divider(
                      color: Colors.grey.withOpacity(0.3),
                      height: 1,
                      thickness: 0.5,
                    ),
                  ],

                  // Regular actions
                  ...regularActions.asMap().entries.map((entry) {
                    final index = entry.key;
                    final action = entry.value;
                    final isLast = index == regularActions.length - 1;

                    return Column(
                      children: [
                        _buildActionButton(
                          action: action,
                          isFirst: (title == null && message == null) && index == 0,
                          isLast: isLast,
                        ),
                        if (!isLast)
                          Divider(
                            color: Colors.grey.withOpacity(0.3),
                            height: 1,
                            thickness: 0.5,
                          ),
                      ],
                    );
                  }).toList(),
                ],
              ),
            ),

            // Cancel button (separate)
            if (cancelActions.isNotEmpty) ...[
              const SizedBox(height: 8),
              ...cancelActions.map((action) => Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: _buildActionButton(
                  action: action,
                  isFirst: true,
                  isLast: true,
                ),
              )).toList(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required ActionSheetAction action,
    required bool isFirst,
    required bool isLast,
  }) {
    Color textColor = const Color(0xFF007AFF); // Default blue
    FontWeight fontWeight = FontWeight.w400;

    if (action.isDestructive) {
      textColor = const Color(0xFFFF3B30); // Red for destructive actions
    } else if (action.isCancel) {
      textColor = const Color(0xFF007AFF); // Blue for cancel
      fontWeight = FontWeight.w600; // Cancel is bold
    } else if (action.color != null) {
      textColor = action.color!;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.vertical(
          top: isFirst ? const Radius.circular(14) : Radius.zero,
          bottom: isLast ? const Radius.circular(14) : Radius.zero,
        ),
        onTap: action.onPressed,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 18),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (action.icon != null) ...[
                Icon(
                  action.icon,
                  color: textColor,
                  size: 22,
                ),
                const SizedBox(width: 12),
              ],
              Text(
                action.title,
                style: TextStyle(
                  color: textColor,
                  fontSize: 20,
                  fontWeight: fontWeight,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 