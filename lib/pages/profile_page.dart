import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import '../services/user_preferences.dart';
import '../providers/language_provider.dart';
import '../theme/app_design_system.dart';
import '../components/grid_background.dart';
import 'edit_username_page.dart';
import 'webview_page.dart';
import 'language_selection_page.dart';
import 'avatar_selection_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  String _username = 'UserName';
  String _version = '';
  String _avatarPath = '';

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadAppVersion();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _username = UserPreferences.instance.getUsername();
      _avatarPath = UserPreferences.instance.getAvatar();
    });
  }

  Future<void> _loadAppVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _version = '${packageInfo.version} (${packageInfo.buildNumber})';
      });
    } catch (e) {
      setState(() {
        _version = '1.0.0';
      });
    }
  }

  Future<void> _editUsername() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditUsernamePage(currentUsername: _username),
      ),
    );

    if (result != null) {
      setState(() {
        _username = result;
      });
    }
  }

  Future<void> _selectAvatar() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AvatarSelectionPage(
          currentAvatar: _avatarPath.isNotEmpty ? _avatarPath : null,
        ),
      ),
    );

    if (result != null) {
      await UserPreferences.instance.setAvatar(result);
      setState(() {
        _avatarPath = result;
      });
    }
  }

  Future<void> _showLanguageDialog() async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LanguageSelectionPage(),
      ),
    );
  }

  String _getLanguageDisplayText(LanguageProvider languageProvider) {
    return languageProvider
        .getLanguageName(languageProvider.locale.languageCode);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return Scaffold(
          backgroundColor: AppDesignSystem.backgroundPrimary,
          body: GridBackground(
            gridSize: 35.0,
            gridColor: Colors.black.withAlpha(20), // 更淡的网格线
            strokeWidth: 0.5,
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                    decoration: BoxDecoration(
                      color: AppDesignSystem.profileCardBackground,
                      borderRadius: BorderRadius.all(Radius.circular(50)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 30,
                        ),
                        const SizedBox(width: 5),
                        Text(
                          l10n.profile,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                          ),
                        ),
                        const SizedBox(width: 5),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30),
                  // User Profile Section
                  Container(
                    height: 300,
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: AppDesignSystem.profileUserCardDecoration,
                    child: Stack(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Avatar
                                  GestureDetector(
                                    onTap: _selectAvatar,
                                    child: Stack(
                                      children: [
                                        CircleAvatar(
                                          radius: 60,
                                          backgroundColor: _avatarPath.isNotEmpty
                                              ? Colors.transparent
                                              : AppDesignSystem.primaryYellow,
                                          backgroundImage: _avatarPath.isNotEmpty
                                              ? AssetImage(_avatarPath)
                                              : null,
                                          child: _avatarPath.isEmpty
                                              ? Text(
                                                  _username.isNotEmpty
                                                      ? _username[0].toUpperCase()
                                                      : 'U',
                                                  style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 26,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                )
                                              : null,
                                        ),
                                        Positioned(
                                          bottom: 0,
                                          right: 0,
                                          child: Container(
                                            padding: const EdgeInsets.all(4),
                                            decoration: const BoxDecoration(
                                              color: AppDesignSystem.primaryYellow,
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Icon(
                                              Icons.edit,
                                              size: 16,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  // Username and subtitle
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          'Me',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 50,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 6),
                                        Text(
                                          "Let's chat now!",
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 20,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 35),
                              Row(
                                children: [
                                  // Edit Profile Button
                                  Container(
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        onTap: _editUsername,
                                        borderRadius: BorderRadius.circular(28),
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 12),
                                          decoration: AppDesignSystem
                                              .profileEditButtonDecoration,
                                          child: const Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              SizedBox(width: 15),
                                              Icon(
                                                Icons.edit,
                                                color: Colors.black,
                                                size: 18,
                                              ),
                                              SizedBox(width: 8),
                                              Text(
                                                'Edit profile',
                                                style: TextStyle(
                                                  color: Colors.black,
                                                  fontSize: 20,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              SizedBox(width: 15),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                        // Flower decoration
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Container(
                            child: const Icon(
                              Icons.local_florist_outlined,
                              color: AppDesignSystem.primaryYellow,
                              size: 90,
                            ),
                          ),
                        ),
                        Positioned(
                            bottom: 30,
                            right: 100,
                            child: Container(
                              child: const Icon(
                                Icons.star,
                                color: AppDesignSystem.primaryYellow,
                                size: 30,
                              ),
                            )),
                        Positioned(
                            bottom: 130,
                            right: 15,
                            child: Container(
                              child: const Icon(
                                Icons.star,
                                color: AppDesignSystem.primaryYellow,
                                size: 30,
                              ),
                            ))
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Settings Items
                  _buildSettingsItem(
                    icon: Icons.language,
                    title: l10n.language,
                    subtitle: _getLanguageDisplayText(languageProvider),
                    onTap: _showLanguageDialog,
                    showArrow: true,
                  ),

                  const SizedBox(height: 16),

                  _buildSettingsItem(
                    icon: Icons.privacy_tip,
                    title: 'Privacy Policy',
                    subtitle: '',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const WebViewPage(
                            url: 'https://agent.nahki.online/privacy',
                            title: 'Privacy Policy',
                          ),
                        ),
                      );
                    },
                    showArrow: true,
                  ),

                  const SizedBox(height: 16),

                  _buildSettingsItem(
                    icon: Icons.description,
                    title: 'Terms of Use',
                    subtitle: '',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const WebViewPage(
                            url:
                                'https://www.apple.com/legal/internet-services/itunes/dev/stdeula',
                            title: 'Terms of Service',
                          ),
                        ),
                      );
                    },
                    showArrow: true,
                  ),

                  const SizedBox(height: 16),

                  _buildSettingsItem(
                    icon: Icons.info,
                    title: 'App Version',
                    subtitle: _version.isNotEmpty ? _version : 'v.1.0.8',
                    onTap: null,
                    showArrow: false,
                  ),

                  const SizedBox(height: 60),
                ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    bool showArrow = true,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(28),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 18),
            decoration: AppDesignSystem.profileSettingsItemDecoration,
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.black,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      color: AppDesignSystem.textPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (subtitle.isNotEmpty && !showArrow)
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: AppDesignSystem.textSecondary,
                      fontSize: 14,
                    ),
                  ),
                if (subtitle.isNotEmpty && showArrow)
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: AppDesignSystem.textSecondary,
                      fontSize: 14,
                    ),
                  ),
                if (showArrow)
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.black,
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
