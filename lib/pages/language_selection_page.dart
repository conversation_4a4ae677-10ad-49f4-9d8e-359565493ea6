import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/language_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class LanguageSelectionPage extends StatelessWidget {
  const LanguageSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          l10n.language,
          style: const TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
      ),
      body: Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildLanguageOption(
                context,
                languageProvider,
                'en',
                l10n.english,
                'English',
              ),
              _buildLanguageOption(
                context,
                languageProvider,
                'zh',
                l10n.traditionalChinese,
                '中文',
              ),
              _buildLanguageOption(
                context,
                languageProvider,
                'ar',
                l10n.arabic,
                'العربية',
              ),
              _buildLanguageOption(
                context,
                languageProvider,
                'hi',
                l10n.hindi,
                'हिंदी',
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    LanguageProvider languageProvider,
    String languageCode,
    String translatedName,
    String nativeName,
  ) {
    final isSelected = languageProvider.locale.languageCode == languageCode;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: isSelected ? const Color(0xFF0A84FF).withOpacity(0.1) : const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(12),
        border: isSelected 
            ? Border.all(color: const Color(0xFF0A84FF), width: 1)
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () async {
            await languageProvider.changeLanguage(languageCode);
            // 延迟一点让用户看到选中效果
            await Future.delayed(const Duration(milliseconds: 200));
            if (context.mounted) {
              Navigator.pop(context);
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        translatedName,
                        style: TextStyle(
                          color: isSelected ? const Color(0xFF0A84FF) : Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        nativeName,
                        style: TextStyle(
                          color: isSelected ? const Color(0xFF0A84FF).withOpacity(0.7) : Colors.grey[400],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? const Color(0xFF0A84FF) : Colors.transparent,
                    border: Border.all(
                      color: isSelected ? const Color(0xFF0A84FF) : Colors.grey,
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 