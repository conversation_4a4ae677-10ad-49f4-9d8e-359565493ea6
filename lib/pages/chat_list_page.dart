import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:easy_refresh/easy_refresh.dart';
import '../components/grid_background.dart';
import '../models/user.dart';
import '../models/chat.dart';
import '../providers/chat_provider.dart';
import '../services/database_service.dart';
import '../services/ai_data_service.dart';

import '../theme/app_design_system.dart';
import 'chat_page.dart';
import 'ai_detail_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ChatListPage extends StatefulWidget {
  const ChatListPage({super.key});

  @override
  State<ChatListPage> createState() => _ChatListPageState();
}

class _ChatListPageState extends State<ChatListPage>
    with AutomaticKeepAliveClientMixin {
  late EasyRefreshController _refreshController;

  List<User> _recommendedUsers = [];
  bool _isRefreshing = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _refreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    _loadRecommendedUsers();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ChatProvider>().loadChats();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次页面显示时刷新聊天列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<ChatProvider>().loadChats();
      }
    });
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _loadRecommendedUsers() async {
    final users = await AIDataService().getRecommendedUsers(count: 8);
    if (mounted) {
      setState(() {
        _recommendedUsers = users;
      });
    }
  }

  Future<void> _refreshRecommendedUsers() async {
    // Refresh the AI data and get new random users
    await AIDataService().refreshData();
    await _loadRecommendedUsers();
  }

  Future<void> _onRefresh() async {
    if (_isRefreshing) return;

    if (mounted) {
      setState(() {
        _isRefreshing = true;
      });
    }

    try {
      await Future.wait([
        _refreshRecommendedUsers(),
        context.read<ChatProvider>().loadChats(),
      ]);

      // Add a small delay to simulate network request
      await Future.delayed(const Duration(milliseconds: 500));

      _refreshController.finishRefresh(IndicatorResult.success);
    } catch (e) {
      _refreshController.finishRefresh(IndicatorResult.fail);
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      body: GridBackground(
          gridSize: 35.0,
          gridColor: Colors.black.withAlpha(20), // 更淡的网格线
          strokeWidth: 0.5,
          child: SafeArea(
            child: EasyRefresh.builder(
              controller: _refreshController,
              onRefresh: _onRefresh,
              childBuilder: (context, physics) {
                return CustomScrollView(
                  physics: physics,
                  slivers: [
                    SliverToBoxAdapter(
                      child: Column(
                        children: [
                          _buildHeader(),
                          // _buildRecommendedSection(),
                        ],
                      ),
                    ),
                    _buildSliverChatList(),
                  ],
                );
              },
              header: ClassicHeader(
                dragText: AppLocalizations.of(context)!.pullToRefresh,
                armedText: AppLocalizations.of(context)!.releaseToRefresh,
                readyText: AppLocalizations.of(context)!.refreshing,
                processingText: AppLocalizations.of(context)!.refreshing,
                processedText: AppLocalizations.of(context)!.refreshCompleted,
                noMoreText: AppLocalizations.of(context)!.noMoreData,
                failedText: AppLocalizations.of(context)!.refreshFailed,
                messageText: AppLocalizations.of(context)!.lastUpdatedAt,
                backgroundColor: Colors.transparent,
                textStyle: TextStyle(color: Colors.grey.withOpacity(0.7)),
                messageStyle: TextStyle(
                    color: Colors.grey.withOpacity(0.5), fontSize: 12),
                iconTheme: IconThemeData(color: Colors.grey.withOpacity(0.7)),
                showMessage: false,
              ),
            ),
          )),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 30),
      child: Row(
        children: [
          Text(
            AppLocalizations.of(context)!.message,
            style: const TextStyle(
              color: AppDesignSystem.textPrimary,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendedSection() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.star,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      AppLocalizations.of(context)!.recommend,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 15),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            itemCount: _recommendedUsers.length,
            itemBuilder: (context, index) {
              final user = _recommendedUsers[index];
              return _buildRecommendedUserCard(user);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendedUserCard(User user) {
    return Container(
      margin: const EdgeInsets.only(right: 15),
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AIDetailPage(aiUser: user),
                ),
              );
            },
            child: Stack(
              children: [
                CircleAvatar(
                  radius: 28,
                  backgroundImage: AssetImage(user.avatar),
                ),
                if (user.isOnline)
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 1),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => _startChat(user),
            child: Text(
              user.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverChatList() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        final chats = chatProvider.chats;

        if (chats.isEmpty) {
          return SliverFillRemaining(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 64,
                    color: Colors.grey.withOpacity(0.3),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.noChatsYet,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.grey.withOpacity(0.6),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final chat = chats[index];
              final isMiddle = index != 0 && index != chats.length - 1;
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 25),
                child: _buildChatListItem(chat, index, chats.length),
              );
            },
            childCount: chats.length,
          ),
        );
      },
    );
  }

  Widget _buildChatListItem(Chat chat, int index, int length) {
    return FutureBuilder<User?>(
      future: DatabaseService().getUserById(chat.aiId),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox();
        }

        final user = snapshot.data!;
        final time = _formatChatTime(
            chat.lastMessageTime, AppLocalizations.of(context)!);

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(index == 0 ? 20 : 0), topRight: Radius.circular(index == 0 ? 20 : 0), bottomLeft: Radius.circular(index == length - 1 ? 20 : 0), bottomRight: Radius.circular(index == length - 1 ? 20 : 0)),
            border: Border.all(
              color: Colors.grey.withOpacity(0.05),
              width: 0.5,
            ),
          ),
          child: Material(
            color: AppDesignSystem.backgroundCard,
            borderRadius: BorderRadius.only(topLeft: Radius.circular(index == 0 ? 20 : 0), topRight: Radius.circular(index == 0 ? 20 : 0), bottomLeft: Radius.circular(index == length - 1 ? 20 : 0), bottomRight: Radius.circular(index == length - 1 ? 20 : 0)),
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: () => _startChat(user),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AIDetailPage(aiUser: user),
                          ),
                        );
                      },
                      child: Stack(
                        children: [
                          CircleAvatar(
                            radius: 28,
                            backgroundImage: AssetImage(user.avatar),
                          ),
                          if (user.isOnline)
                            Positioned(
                              right: 0,
                              bottom: 2,
                              child: Container(
                                width: 14,
                                height: 14,
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                  border:
                                      Border.all(color: Colors.white, width: 1),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            user.name,
                            style: const TextStyle(
                              color: AppDesignSystem.textPrimary,
                              fontSize: 21,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            chat.lastMessage,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: AppDesignSystem.textPrimary,
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          time,
                          style: TextStyle(
                            color: AppDesignSystem.textPrimary,
                            fontSize: 14,
                          ),
                        ),
                        if (chat.unreadCount > 0)
                          Container(
                            margin: const EdgeInsets.only(top: 5),
                            padding: const EdgeInsets.all(6),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: Text(
                              chat.unreadCount.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _startChat(User user) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(aiUser: user),
      ),
    );
    // 从聊天页面返回后，刷新聊天列表
    if (mounted) {
      context.read<ChatProvider>().loadChats();
    }
  }

  // 智能时间格式化函数
  String _formatChatTime(DateTime messageTime, AppLocalizations l10n) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate =
        DateTime(messageTime.year, messageTime.month, messageTime.day);

    // 今天的消息：只显示时间
    if (messageDate == today) {
      return DateFormat('HH:mm').format(messageTime);
    }

    // 昨天的消息：显示"昨天"
    if (messageDate == yesterday) {
      return l10n.yesterday;
    }

    // 今年的消息：显示"月-日"
    if (messageTime.year == now.year) {
      return DateFormat('MM-dd').format(messageTime);
    }

    // 跨年的消息：显示"年-月-日"
    return DateFormat('yyyy-MM-dd').format(messageTime);
  }
}
