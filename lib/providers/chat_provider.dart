import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/message.dart';
import '../models/chat.dart';
import '../services/database_service.dart';
import '../services/ai_chat_service.dart';
import 'language_provider.dart';

class ChatProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final AIChatService _aiChatService = AIChatService();
  List<Chat> _chats = [];
  List<Message> _currentMessages = [];
  User? _currentUser;
  User? _currentAIUser; // 追踪当前AI用户
  AIChatConfig _currentAIConfig = AIChatConfig();
  bool _isLoading = false;

  List<Chat> get chats => _chats;
  List<Message> get currentMessages => _currentMessages;
  User? get currentUser => _currentUser;
  User? get currentAIUser => _currentAIUser;
  AIChatConfig get currentAIConfig => _currentAIConfig;
  bool get isLoading => _isLoading;

  Future<void> loadChats() async {
    _chats = await _databaseService.getChats();
    notifyListeners();
  }

  // 更新内存中的聊天列表
  Future<void> _updateChatInList(Chat updatedChat) async {
    final existingIndex = _chats.indexWhere((chat) => chat.id == updatedChat.id);
    
    if (existingIndex != -1) {
      // 更新现有聊天
      _chats[existingIndex] = updatedChat;
      if (kDebugMode) {
        print('Updated existing chat: ${updatedChat.id} with message: ${updatedChat.lastMessage}');
      }
    } else {
      // 添加新聊天到列表开头
      _chats.insert(0, updatedChat);
      if (kDebugMode) {
        print('Added new chat: ${updatedChat.id} with message: ${updatedChat.lastMessage}');
      }
    }
    
    // 按最后消息时间重新排序
    _chats.sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
  }

  Future<void> loadMessagesForChat(String chatId) async {
    _currentMessages = await _databaseService.getMessagesForChat(chatId);
    // 切换到新的聊天对话时，清除AI历史以确保角色设置正确
    _aiChatService.clearHistory();
    notifyListeners();
  }

  // 设置AI配置
  void setAIConfig(User? aiUser, {AILanguage? language, LanguageProvider? languageProvider}) {
    if (aiUser != null) {
      // 使用User模型的getAIRole方法
      AIRole role = aiUser.getAIRole();
      
      // 如果没有指定语言，使用LanguageProvider的当前语言
      AILanguage targetLanguage = language ?? languageProvider?.currentAILanguage ?? AILanguage.english;
      
      // 检查是否切换了用户或语言
      bool userChanged = _currentAIUser?.id != aiUser.id;
      bool roleChanged = role != _currentAIConfig.role;
      bool languageChanged = targetLanguage != _currentAIConfig.language;
      
      // 更新当前AI用户
      _currentAIUser = aiUser;
      
      _currentAIConfig = AIChatConfig.fast(
        role: role,
        language: targetLanguage,
      );
      
      // 如果用户、角色或语言切换了，清除对话历史以确保新设置生效
      if (userChanged || roleChanged || languageChanged) {
        _aiChatService.clearHistory();
        if (kDebugMode) {
          print('AI config changed - clearing history. User: ${aiUser.name}, Role: ${role.displayName}, Language: ${targetLanguage.name}');
        }
      }
    }
    notifyListeners();
  }



  Future<void> sendMessage(String chatId, String content, String senderId, String receiverId) async {
    final message = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      chatId: chatId,
      senderId: senderId,
      receiverId: receiverId,
      content: content,
      timestamp: DateTime.now(),
    );

    await _databaseService.insertMessage(message);
    _currentMessages.add(message);

    // Update chat with last message
    final chat = Chat(
      id: chatId,
      userId: 'user',
      aiId: receiverId,
      lastMessage: content,
      lastMessageTime: DateTime.now(),
    );
    await _databaseService.insertOrUpdateChat(chat);

    // Update the chat list in memory
    await _updateChatInList(chat);

    notifyListeners();
  }

  Future<void> sendAIResponse(String chatId, String content, String senderId, String receiverId, User? aiUser, {LanguageProvider? languageProvider}) async {
    _isLoading = true;
    notifyListeners();

    try {
      // 设置AI配置
      if (aiUser != null) {
        setAIConfig(aiUser, languageProvider: languageProvider);
      }

      // 使用AI服务获取智能回复
      String aiResponse;
      
      try {
        aiResponse = await _aiChatService.sendMessage(content, _currentAIConfig);
      } catch (e) {
        // 如果AI服务失败，直接显示服务不可用
        if (kDebugMode) {
          print('AI Service Error: $e');
        }
        aiResponse = "Service unavailable";
      }

      final message = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: chatId,
        senderId: senderId,
        receiverId: receiverId,
        content: aiResponse,
        timestamp: DateTime.now(),
      );

      await _databaseService.insertMessage(message);
      _currentMessages.add(message);

      // Update chat with last message
      final chat = Chat(
        id: chatId,
        userId: 'user',
        aiId: senderId,
        lastMessage: aiResponse,
        lastMessageTime: DateTime.now(),
      );
      await _databaseService.insertOrUpdateChat(chat);

      // Update the chat list in memory
      await _updateChatInList(chat);

    } catch (e) {
      if (kDebugMode) {
        print('Error sending AI response: $e');
      }
      
      // 发送错误提示消息
      final errorMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        chatId: chatId,
        senderId: senderId,
        receiverId: receiverId,
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: DateTime.now(),
      );

      await _databaseService.insertMessage(errorMessage);
      _currentMessages.add(errorMessage);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 清除AI聊天历史
  void clearAIHistory() {
    _aiChatService.clearHistory();
  }

  // 清除当前对话的所有消息和AI历史
  void clearCurrentConversation() {
    _currentMessages.clear();
    _aiChatService.clearHistory();
    notifyListeners();
  }


} 