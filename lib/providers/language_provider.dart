import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/ai_chat_service.dart';

class LanguageProvider with ChangeNotifier {
  static const String _languageKey = 'selected_language';
  
  // 默认语言是英语
  Locale _locale = const Locale('en');
  
  Locale get locale => _locale;
  
  // 获取当前语言对应的AILanguage
  AILanguage get currentAILanguage {
    switch (_locale.languageCode) {
      case 'zh':
        return AILanguage.traditional;
      case 'ar':
        return AILanguage.arabic;
      case 'hi':
        return AILanguage.hindi;
      default:
        return AILanguage.english;
    }
  }
  
  // 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('en'), // 英语
    Locale('zh'), // 中文
    Locale('ar'), // 阿拉伯语
    Locale('hi'), // 印地语
  ];
  
  // 语言名称映射
  static const Map<String, String> languageNames = {
    'en': 'English',
    'zh': '中文',
    'ar': 'العربية',
    'hi': 'हिंदी',
  };
  
  // 初始化语言设置
  Future<void> initializeLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLanguage = prefs.getString(_languageKey);
    
    if (savedLanguage != null) {
      _locale = Locale(savedLanguage);
    } else {
      // 如果没有保存的语言，使用系统语言或默认英语
      final systemLocale = WidgetsBinding.instance.platformDispatcher.locale;
      if (supportedLocales.any((locale) => locale.languageCode == systemLocale.languageCode)) {
        _locale = Locale(systemLocale.languageCode);
      } else {
        _locale = const Locale('en'); // 默认英语
      }
    }
    
    notifyListeners();
  }
  
  // 切换语言
  Future<void> changeLanguage(String languageCode) async {
    if (_locale.languageCode == languageCode) return;
    
    _locale = Locale(languageCode);
    
    // 保存到本地存储
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
    
    notifyListeners();
  }
  
  // 获取语言显示名称
  String getLanguageName(String languageCode) {
    return languageNames[languageCode] ?? languageCode;
  }
  
  // 检查是否为RTL语言
  bool get isRTL => _locale.languageCode == 'ar';
} 